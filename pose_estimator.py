import open3d as o3d
import numpy as np
import copy
import os

class PoseEstimator:
    """
    A class for performing registration between source and target point clouds.
    The process includes preprocessing, coarse registration, and fine registration.
    """

    def __init__(self, source_pcd, target_pcd, voxel_size=0.05):
        """
        Initialize PoseEstimator.

        Args:
            source_pcd (o3d.geometry.PointCloud): Source point cloud.
            target_pcd (o3d.geometry.PointCloud): Target point cloud.
            voxel_size (float): Voxel size for downsampling.
        """
        if not isinstance(source_pcd, o3d.geometry.PointCloud) or not isinstance(target_pcd, o3d.geometry.PointCloud):
            raise ValueError("source_pcd and target_pcd must be open3d.geometry.PointCloud objects.")

        self.source_pcd = source_pcd
        self.target_pcd = target_pcd
        self.voxel_size = voxel_size
        
        self.transformation = np.identity(4)
        self.fitness = 0.0
        self.inlier_rmse = 0.0

    def _preprocess_point_cloud(self, pcd):
        """
        Preprocess point cloud: downsampling, outlier removal, and normal/feature estimation.

        Args:
            pcd (o3d.geometry.PointCloud): Input point cloud.

        Returns:
            tuple: A tuple containing:
                - o3d.geometry.PointCloud: Downsampled point cloud.
                - o3d.pipelines.registration.Feature: FPFH feature vectors.
        """
        print(f":: Downsampling with voxel size {self.voxel_size:.3f}.")
        print(f":: Point cloud size: {len(pcd.points)}")
        pcd_down = pcd.voxel_down_sample(self.voxel_size)
        print(f":: Downsampled point cloud size: {len(pcd_down.points)}")

        radius_normal = self.voxel_size * 2
        print(f":: Estimating normals with search radius {radius_normal:.3f}.")
        pcd_down.estimate_normals(
            o3d.geometry.KDTreeSearchParamHybrid(radius=radius_normal, max_nn=30))

        radius_feature = self.voxel_size * 5
        print(f":: Computing FPFH features with search radius {radius_feature:.3f}.")
        pcd_fpfh = o3d.pipelines.registration.compute_fpfh_feature(
            pcd_down,
            o3d.geometry.KDTreeSearchParamHybrid(radius=radius_feature, max_nn=100))
        
        return pcd_down, pcd_fpfh

    def _execute_coarse_registration(self, source_down, target_down, source_fpfh, target_fpfh):
        """
        Perform coarse registration using RANSAC based on feature matching.
        """
        distance_threshold = self.voxel_size * 1.5
        print(":: RANSAC registration on downsampled point clouds.")
        print(f"   Since downsampling voxel size is {self.voxel_size:.3f}, using a liberal distance threshold {distance_threshold:.3f}.")
        
        result = o3d.pipelines.registration.registration_ransac_based_on_feature_matching(
            source_down, target_down, source_fpfh, target_fpfh, True,
            distance_threshold,
            o3d.pipelines.registration.TransformationEstimationPointToPoint(False),
            3, [
                o3d.pipelines.registration.CorrespondenceCheckerBasedOnEdgeLength(0.9),
                o3d.pipelines.registration.CorrespondenceCheckerBasedOnDistance(distance_threshold)
            ], o3d.pipelines.registration.RANSACConvergenceCriteria(100000, 0.999))
        
        return result.transformation

    def _execute_fine_registration(self, source_pcd, target_pcd, initial_transform):
        """
        Perform fine registration using point-to-plane ICP.

        Args:
            source_pcd (o3d.geometry.PointCloud): Source point cloud for ICP (usually original).
            target_pcd (o3d.geometry.PointCloud): Target point cloud for ICP (usually original).
            initial_transform (np.ndarray): Initial transformation from coarse registration.

        Returns:
            o3d.pipelines.registration.RegistrationResult: ICP registration result.
        """
        distance_threshold = self.voxel_size * 0.4
        print(":: Applying point-to-plane ICP on original point clouds.")
        print(f"   (Using a conservative distance threshold {distance_threshold:.3f}.)")
        
        # Compute normals for original point clouds for ICP
        if not source_pcd.has_normals():
            source_pcd.estimate_normals()
        if not target_pcd.has_normals():
            target_pcd.estimate_normals()

        result = o3d.pipelines.registration.registration_icp(
            source_pcd, target_pcd, distance_threshold, initial_transform,
            o3d.pipelines.registration.TransformationEstimationPointToPlane(),
            o3d.pipelines.registration.ICPConvergenceCriteria(relative_fitness=1e-6, relative_rmse=1e-6, max_iteration=2000))
            
        return result

    def run(self):
        """
        Execute the complete registration pipeline.
        """
        print("1. Starting data preprocessing...")
        source_down, source_fpfh = self._preprocess_point_cloud(self.source_pcd)
        target_down, target_fpfh = self._preprocess_point_cloud(self.target_pcd)

        print("\n2. Starting coarse registration...")
        coarse_transform = self._execute_coarse_registration(source_down, target_down, source_fpfh, target_fpfh)

        print("\n3. Starting fine registration...")
        fine_reg_result = self._execute_fine_registration(self.source_pcd, self.target_pcd, coarse_transform)
        
        self.transformation = fine_reg_result.transformation
        self.fitness = fine_reg_result.fitness
        self.inlier_rmse = fine_reg_result.inlier_rmse

        print("\n4. Registration complete.")
        print(f"   Fitness: {self.fitness:.4f}")
        print(f"   Inlier RMSE: {self.inlier_rmse:.4f}")

        return self.transformation

    def visualize_registration(self, save_dir="out/T1", visualize_merged_clouds=True):
        """
        Visualize registration results with option to save merged point clouds before/after alignment.

        Args:
            visualize_merged_clouds (bool): If True, saves merged point cloud files.
        """
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)

        print("Visualizing registration results...")
        source_transformed = copy.deepcopy(self.source_pcd)
        source_transformed.transform(self.transformation)

        # Assign colors to source and target point clouds
        self.source_pcd.paint_uniform_color([1, 0, 0]) # red
        self.target_pcd.paint_uniform_color([0, 0, 1]) # blue
        source_transformed.paint_uniform_color([1, 0, 0]) # red

        original_merged = self.source_pcd + self.target_pcd
        o3d.io.write_point_cloud(os.path.join(save_dir, "original_alignment.ply"), original_merged)
        print(f"Saved original aligned point cloud to {os.path.join(save_dir, 'original_alignment.ply')}")

        registered_merged = source_transformed + self.target_pcd
        o3d.io.write_point_cloud(os.path.join(save_dir, "registered_alignment.ply"), registered_merged)
        print(f"Saved registered aligned point cloud to {os.path.join(save_dir, 'registered_alignment.ply')}")


        if visualize_merged_clouds:
            # Original alignment
            print("Showing interactive window: Original alignment...")
            o3d.visualization.draw_geometries([self.source_pcd, self.target_pcd],
                                            window_name='Original alignment',
                                            zoom=0.4559, front=[0.6452, -0.3036, -0.7011],
                                            lookat=[1.9892, 2.0208, 1.8945], up=[-0.2779, -0.9529, 0.1556])

            # Registered alignment
            print("Showing interactive window: Registered alignment...")
            o3d.visualization.draw_geometries([source_transformed, self.target_pcd],
                                            window_name='Registered alignment',
                                            zoom=0.4559, front=[0.6452, -0.3036, -0.7011],
                                            lookat=[1.9892, 2.0208, 1.8945], up=[-0.2779, -0.9529, 0.1556])

    def save_results(self, transform_path="transform.npy", pcd_path="transformed_source.pcd"):
        """
        Save transformation matrix and transformed source point cloud.

        Args:
            transform_path (str): File path to save transformation matrix.
            pcd_path (str): File path to save transformed point cloud.
        """
        print(f"Saving transformation matrix to {transform_path}")
        np.save(transform_path, self.transformation)

        source_transformed = copy.deepcopy(self.source_pcd)
        source_transformed.transform(self.transformation)
        print(f"Saving transformed source point cloud to {pcd_path}")
        o3d.io.write_point_cloud(pcd_path, source_transformed)


if __name__ == "__main__":
    source_pcd = o3d.io.read_point_cloud("source.pcd")
    target_pcd = o3d.io.read_point_cloud("target.pcd")
    pose_estimator = PoseEstimator(source_pcd, target_pcd)
    pose_estimator.run()
    pose_estimator.visualize_registration(save_dir="out/T1", visualize_merged_clouds=False)
    # pose_estimator.save_results()